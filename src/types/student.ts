// Student types for the application

// Base interface for documents with timestamps
interface BaseDocument {
  created_at: string;
  updated_at: string;
}

export interface Student extends BaseDocument {
  id: string;
  student_id: string;
  name: string;
  date_of_birth: string;
  date_of_registration: string;
  nationality: string;
  gender: string;
  address: string;
  mobile_number: string;
  whatsapp_number: string;
  parent_name: string;
  parent_mobile: string;
  parent_whatsapp: string;
  parent_email: string;
  parent_occupation: string;
  passport_picture: string | null;
  course_id: string;
  level_id: string;
  enrollment_status: string;
  payment_status?: string;
  created_at: string;
  updated_at: string;
  
  // Additional fields that might be present
  course_name?: string;
  level_name?: string;
  email?: string;
  
  login_details?: {
    user_id: string;
    email: string;
    dashboard_access: boolean;
    created_at?: Date;
    updated_at?: Date;
  };
  course?: {
    id: string;
    name: string;
    code: string;
    description: string | null;
  };
  level?: {
    id: string;
    name: string;
    code: string | null;
    description: string | null;
  };
}
