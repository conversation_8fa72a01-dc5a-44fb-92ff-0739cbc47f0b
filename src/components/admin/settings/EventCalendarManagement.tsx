import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Trash2, Pencil, AlertCircle, Calendar as CalendarIcon, Clock, MapPin } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { format, parse } from 'date-fns';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar } from "@/components/ui/calendar";
import { 
  getEvents, 
  createEvent, 
  updateEvent, 
  deleteEvent 
} from '@/api/events';
import type { Event } from '@/api/events';

// Interface for the component's state
interface EventFormState extends Omit<Event, 'id' | 'created_at' | 'updated_at'> {}

const EventCalendarManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [newEvent, setNewEvent] = useState<EventFormState>({
    title: '',
    date: format(new Date(), 'yyyy-MM-dd'),
    time: '9:00 AM',
    location: '',
    category: 'academic',
    description: ''
  });
  
  const [eventToEdit, setEventToEdit] = useState<Event | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [editDate, setEditDate] = useState<Date | undefined>(undefined);
  const [activeTab, setActiveTab] = useState('list');
  
  // Fetch events
  const { data: events = [] } = useQuery({
    queryKey: ['events'],
    queryFn: getEvents
  });
  
  // Update date in form when date picker changes
  useEffect(() => {
    if (selectedDate) {
      setNewEvent(prev => ({
        ...prev,
        date: format(selectedDate, 'yyyy-MM-dd')
      }));
    }
  }, [selectedDate]);
  
  // Update date in edit form when date picker changes
  useEffect(() => {
    if (editDate && eventToEdit) {
      setEventToEdit({
        ...eventToEdit,
        date: format(editDate, 'yyyy-MM-dd')
      });
    }
  }, [editDate]);
  
  // Set edit date when editing an event
  useEffect(() => {
    if (eventToEdit && eventToEdit.date) {
      setEditDate(parse(eventToEdit.date, 'yyyy-MM-dd', new Date()));
    }
  }, [eventToEdit]);
  
  const handleInputChange = (field: keyof EventFormState, value: any) => {
    setNewEvent(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  const handleEditInputChange = (field: keyof Event, value: any) => {
    if (eventToEdit) {
      setEventToEdit({
        ...eventToEdit,
        [field]: value
      });
    }
  };
  
  const handleAddEvent = async () => {
    if (!newEvent.title.trim()) {
      toast({
        title: "Error",
        description: "Title is required",
        variant: "destructive"
      });
      return;
    }
    
    if (!newEvent.location?.trim()) {
      toast({
        title: "Error",
        description: "Location is required",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await createEvent(newEvent);
      
      // Reset form
      setNewEvent({
        title: '',
        date: format(new Date(), 'yyyy-MM-dd'),
        time: '9:00 AM',
        location: '',
        category: 'academic',
        description: ''
      });
      
      // Reset date picker
      setSelectedDate(new Date());
      
      // Refresh events list
      queryClient.invalidateQueries({ queryKey: ['events'] });
      
      toast({
        title: "Success",
        description: "Event created successfully"
      });
    } catch (error) {
      console.error('Error creating event:', error);
      toast({
        title: "Error",
        description: "Failed to create event",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleUpdateEvent = async () => {
    if (!eventToEdit || !eventToEdit.id) {
      toast({
        title: "Error",
        description: "Event not found",
        variant: "destructive"
      });
      return;
    }
    
    if (!eventToEdit.title.trim()) {
      toast({
        title: "Error",
        description: "Title is required",
        variant: "destructive"
      });
      return;
    }
    
    if (!eventToEdit.location?.trim()) {
      toast({
        title: "Error",
        description: "Location is required",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const { id, created_at, updated_at, ...eventData } = eventToEdit;
      await updateEvent(id, eventData);
      
      // Reset form
      setEventToEdit(null);
      
      // Refresh events list
      queryClient.invalidateQueries({ queryKey: ['events'] });
      
      toast({
        title: "Success",
        description: "Event updated successfully"
      });
    } catch (error) {
      console.error('Error updating event:', error);
      toast({
        title: "Error",
        description: "Failed to update event",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleDeleteEvent = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this event?')) {
      return;
    }
    
    try {
      await deleteEvent(id);
      
      // Refresh events list
      queryClient.invalidateQueries({ queryKey: ['events'] });
      
      toast({
        title: "Success",
        description: "Event deleted successfully"
      });
    } catch (error) {
      console.error('Error deleting event:', error);
      toast({
        title: "Error",
        description: "Failed to delete event",
        variant: "destructive"
      });
    }
  };
  
  // Helper function to get category badge
  const getCategoryBadge = (category: string) => {
    switch (category) {
      case 'exam':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Exam</Badge>;
      case 'academic':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Academic</Badge>;
      case 'meeting':
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">Meeting</Badge>;
      case 'holiday':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Holiday</Badge>;
      case 'cultural':
        return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">Cultural</Badge>;
      case 'sports':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Sports</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Other</Badge>;
    }
  };
  
  // Get events for a specific date
  const getEventsForDate = (date: Date) => {
    const dateString = format(date, 'yyyy-MM-dd');
    return events.filter(event => event.date === dateString);
  };
  
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Event & Calendar Management</h1>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="list">List View</TabsTrigger>
          <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          <TabsTrigger value="add">Add Event</TabsTrigger>
        </TabsList>
        
        <TabsContent value="list" className="space-y-6">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Upcoming Events</h2>
            
            {events.length === 0 ? (
              <div className="text-center py-8">
                <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-lg font-medium">No events found</p>
                <p className="text-gray-500">Create your first event using the Add Event tab.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {events.map((event) => (
                  <Card key={event.id} className="overflow-hidden">
                    <div className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium text-lg">{event.title}</h3>
                          <div className="flex items-center gap-2 mt-2 text-sm text-gray-500">
                            <CalendarIcon className="h-4 w-4" />
                            <span>{new Date(event.date).toLocaleDateString()}</span>
                            <span>•</span>
                            <Clock className="h-4 w-4" />
                            <span>{event.time}</span>
                          </div>
                          <div className="flex items-center gap-2 mt-1 text-sm text-gray-500">
                            <MapPin className="h-4 w-4" />
                            <span>{event.location}</span>
                          </div>
                          <p className="text-sm text-gray-500 mt-2">{event.description}</p>
                        </div>
                        
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setEventToEdit(event);
                              setActiveTab('add');
                            }}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteEvent(event.id)}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </div>
                      
                      <div className="mt-2">
                        {getCategoryBadge(event.category)}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </Card>
        </TabsContent>
        
        <TabsContent value="calendar" className="space-y-6">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Calendar View</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
              <div className="md:col-span-5">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  className="rounded-md border"
                />
              </div>
              
              <div className="md:col-span-2">
                <h3 className="font-medium mb-2">
                  Events on {selectedDate ? format(selectedDate, 'MMMM d, yyyy') : 'Selected Date'}
                </h3>
                
                {selectedDate && getEventsForDate(selectedDate).length === 0 ? (
                  <p className="text-sm text-gray-500">No events scheduled for this date.</p>
                ) : (
                  <div className="space-y-2">
                    {selectedDate && getEventsForDate(selectedDate).map((event) => (
                      <div key={event.id} className="p-2 border rounded-md">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">{event.title}</p>
                            <div className="flex items-center gap-1 text-xs text-gray-500">
                              <Clock className="h-3 w-3" />
                              <span>{event.time}</span>
                            </div>
                            <div className="flex items-center gap-1 text-xs text-gray-500">
                              <MapPin className="h-3 w-3" />
                              <span>{event.location}</span>
                            </div>
                          </div>
                          {getCategoryBadge(event.category)}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </Card>
        </TabsContent>
        
        <TabsContent value="add" className="space-y-6">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">
              {eventToEdit ? 'Edit Event' : 'Add New Event'}
            </h2>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="title">Event Title</Label>
                <Input
                  id="title"
                  value={eventToEdit ? eventToEdit.title : newEvent.title}
                  onChange={(e) => eventToEdit 
                    ? handleEditInputChange('title', e.target.value)
                    : handleInputChange('title', e.target.value)
                  }
                  placeholder="Enter event title"
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="date">Date</Label>
                  <DatePicker
                    selected={eventToEdit ? editDate : selectedDate}
                    onSelect={(date) => {
                      if (eventToEdit) {
                        setEditDate(date);
                      } else {
                        setSelectedDate(date);
                      }
                    }}
                  />
                </div>
                
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={eventToEdit ? eventToEdit.category : newEvent.category}
                    onValueChange={(value) => eventToEdit
                      ? handleEditInputChange('category', value)
                      : handleInputChange('category', value)
                    }
                  >
                    <SelectTrigger id="category">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="academic">Academic</SelectItem>
                      <SelectItem value="exam">Exam</SelectItem>
                      <SelectItem value="meeting">Meeting</SelectItem>
                      <SelectItem value="holiday">Holiday</SelectItem>
                      <SelectItem value="cultural">Cultural</SelectItem>
                      <SelectItem value="sports">Sports</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="time">Time</Label>
                  <Input
                    id="time"
                    value={eventToEdit ? eventToEdit.time : newEvent.time}
                    onChange={(e) => eventToEdit
                      ? handleEditInputChange('time', e.target.value)
                      : handleInputChange('time', e.target.value)
                    }
                    placeholder="e.g., 9:00 AM"
                  />
                </div>
                
                <div className="md:col-span-2">
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={eventToEdit ? eventToEdit.location : newEvent.location}
                    onChange={(e) => eventToEdit
                      ? handleEditInputChange('location', e.target.value)
                      : handleInputChange('location', e.target.value)
                    }
                    placeholder="Enter event location"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={eventToEdit ? eventToEdit.description : newEvent.description}
                  onChange={(e) => eventToEdit
                    ? handleEditInputChange('description', e.target.value)
                    : handleInputChange('description', e.target.value)
                  }
                  placeholder="Enter event description"
                  rows={3}
                />
              </div>
              
              <div className="mt-4 flex gap-2">
                {eventToEdit ? (
                  <>
                    <Button 
                      onClick={handleUpdateEvent}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? 'Updating...' : 'Update Event'}
                    </Button>
                    <Button 
                      variant="secondary"
                      onClick={() => {
                        setEventToEdit(null);
                        setActiveTab('list');
                      }}
                    >
                      Cancel
                    </Button>
                  </>
                ) : (
                  <>
                    <Button 
                      onClick={handleAddEvent}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? 'Creating...' : 'Create Event'}
                    </Button>
                    <Button 
                      variant="secondary"
                      onClick={() => setActiveTab('list')}
                    >
                      Cancel
                    </Button>
                  </>
                )}
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EventCalendarManagement; 